"use client"

import Image from "next/image";

const FiLogIn = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
    <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
    <polyline points="10 17 15 12 10 7"></polyline>
    <line x1="15" y1="12" x2="3" y2="12"></line>
  </svg>
);

function NavBar() {
  return (
    <header className="absolute top-0 left-0 right-0 z-20">
      <div className="mx-auto flex max-w-7xl items-center justify-between px-4 py-5">
        {/* Logo */}
        <div className="flex items-center gap-2">
          <Image src="/logo.png" alt="Logo" width={170} height={170} />
        </div>

        {/* Links */}
        <nav className="hidden items-center gap-8 md:flex">
          <a href="#" className="text-sm font-medium text-white/90 hover:text-white">About</a>
          <a href="#" className="text-sm font-medium text-white/90 hover:text-white">Features</a>
          <a href="#" className="text-sm font-medium text-white/90 hover:text-white">How it Works</a>
          <button className="inline-flex items-center gap-2 rounded-tl-2xl rounded-br-2xl bg-gradient-to-r from-blue-500 to-teal-400 px-4 py-2 text-sm font-semibold text-white shadow-lg hover:from-blue-600 hover:to-teal-500 transition-all duration-200">
            <FiLogIn /> Login
          </button>
        </nav>
      </div>
    </header>
  );
}

export default NavBar;
